class HomeController < ApplicationController
  skip_before_action :verify_authenticity_token
  USER_PASSWORD = "user123"

  def index
    return render json: "登录失败" unless params[:token] == USER_PASSWORD

    @products = Product.search.to_json
    @products_oversea = Product.search(country_code: '美亚').to_json
    @products_oversea_uk = Product.search(country_code: '英亚').to_json
    @products_oversea_euro = Product.search(country_code: '欧亚').to_json
    @products_oversea_japan = Product.search(country_code: '日亚').to_json
  end

  def search
    @products = Product.search(params[:name], country_code: params[:country_code]).to_json
    render json: @products
  end

  def print
    sku = Sku.find_by(id: params[:sku_id])
    if sku
      result = sku.print(params[:print_times], params[:sn_code], params[:country_code], params[:device_code])
      if result['status']
        render json: { success: true, message: '打印成功' }
      else
        render json: { success: false, message: result['message'] || result[:message] }
      end
    else
      render json: { success: false, message: "当前 SKU 不存在" }
    end
  end

  def get_sn_code
    render json: { success: true, message: '获取成功', sn_code: '1234567890' }
  end

  def get_rgb
    product = Product.find_by(name: params[:product_name])
    if product
      sku = product.skus.find_by(name: params[:sku_name])
      if sku
        r_color = sku.r_color
        g_color = sku.g_color
        b_color = sku.b_color
        hex_color = "%02X%02X%02X" % [r_color.to_i, g_color.to_i, b_color.to_i]
        if sku.r_color.blank? && sku.g_color.blank? && sku.b_color.blank?
          render json: { success: false, message: '未填写RGB灯光值，请在后台填写' }
        else
          render json: { success: true, message: '获取成功', hex_color: hex_color }
        end
      else
        render json: { success: false, message: 'SKU不存在' }
      end
    else
      render json: { success: false, message: '当前产品不存在' }
    end
  end
end
