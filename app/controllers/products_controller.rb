class ProductsController < ApplicationController
  before_action :verify_password
  before_action :set_product, only: %i[ show edit update destroy ]

  # GET /products or /products.json
  def index
    @q = Product.ransack(params[:q])
    @products_without_page = @q.result(distinct: true)
    @products = @products_without_page.order("id desc").page(params[:page])

    respond_to do |format|
      format.html
      format.xlsx {
        response.headers["Content-Disposition"] = "attachment; filename=商品列表#{Time.now.strftime("%Y%m%d-%H%M%S")}.xlsx"
      }
    end
  end

  # GET /products/1 or /products/1.json
  def show
  end

  # GET /products/new
  def new
    @product = Product.new
  end

  # GET /products/1/edit
  def edit
  end

  # POST /products or /products.json
  def create
    @product = Product.new(product_params)

    respond_to do |format|
      if @product.save
        format.html { redirect_to products_url, notice: "商品创建成功" }
        format.json { render :show, status: :created, location: @product }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @product.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /products/1 or /products/1.json
  def update
    respond_to do |format|
      if @product.update(product_params)
        format.html { redirect_to products_url, notice: "商品更新成功" }
        format.json { render :show, status: :ok, location: @product }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @product.errors, status: :unprocessable_entity }
      end
    end
  end

  def import
    return redirect_to products_url, notice: "请选择导入文件" if params[:file].blank?

    roo_excel = Roo::Excelx.new(params[:file].path)
    product_ids = []
    sku_ids = []
    success_count = 0
    fail_count = 0
    fail_row_number = []
    total_count = 0
    roo_excel.sheets.each do |sheet_name|
      spreadsheet = roo_excel.sheet(sheet_name)
      express_excel = spreadsheet.parse(headers: true)
      header_keys = express_excel.first.keys
      p "==========="
      p header_keys
      correct_header_keys = ["69码", "EAN", "UPC", "FN码(美亚)", "FN码(英亚)", "FN码(欧亚)", "FN码(日亚)", "条码", "规格", "规格英文名", "货品名称", "货品英文名"]
      return redirect_to products_url, notice: "导入文件的标题有误，请参照核对 EXCEL 第一行标题项" unless (header_keys & correct_header_keys).sort == correct_header_keys.sort

      express_excel.delete_at(0)
      express_excel.each_with_index do |row, index|
        total_count += 1
        begin
          product = Product.find_or_create_by!(name: row["货品名称"])
          product_ids << product.id
          product.update!(name_en: row["货品英文名"])
          sku = product.skus.find_or_create_by!(name: row["规格"])
          sku.update!(name_en: row["规格英文名"], code: row["条码"],
                                              upc_code: row["UPC"], ean_code: row["EAN"],
                                              china_code: row["69码"], fn_code: row["FN码(美亚)"],
                                              fn_code_uk: row["FN码(英亚)"], fn_code_euro: row["FN码(欧亚)"],
                                              fn_code_japan: row["FN码(日亚)"])
          sku_ids << sku.id
          success_count += 1
        rescue
          fail_count += 1
          fail_row_number << index + 2
        end
      end
    end
    Product.where.not(id: product_ids).destroy_all
    Sku.where.not(id: sku_ids).destroy_all
    notice = "商品导入成功，总共 #{total_count} 条，成功 #{success_count} 条，失败 #{fail_count} 条"
    notice << "，请检查失败对应的 EXCEL 行号：#{fail_row_number.join('，')}" if fail_row_number.present?
    redirect_to products_url, notice: notice
  end

  def download_import_template
    send_file "#{Rails.root}/public/商品导入模板.xlsx"
  end

  # DELETE /products/1 or /products/1.json
  def destroy
    @product.destroy

    respond_to do |format|
      format.html { redirect_to products_url, notice: "Product was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_product
      @product = Product.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def product_params
      params.require(:product).permit(:name, :name_en, skus_attributes: [:id, :name, :name_en, :code, :upc_code, :ean_code, :china_code, :fn_code, :fn_code_uk, :fn_code_euro, :fn_code_japan, :r_color, :g_color, :b_color, :_destroy])
    end

    def verify_password
      if current_user.blank?
        redirect_to login_path
      end
    end
end
