wb = xlsx_package.workbook
wb.styles do |style|
  heading = style.add_style alignment: {horizontal: :center}, b: true, sz: 14, bg_color: "0066CC", fg_color: "FF"
  date_cell = style.add_style alignment: {horizontal: :center}, format_code: "yyyy-mm-dd HH:MM:ss", sz: 12
  data = style.add_style alignment: {horizontal: :center}, sz: 12
  wb.add_worksheet(name: "商品列表") do |sheet|
    sheet.add_row %w(货品名称 货品英文名 规格 规格英文名 条码 UPC EAN 69码 FN码(美亚) FN码(欧亚) FN码(英亚) FN码(日亚)), style: heading
    @products_without_page.each do |product|
      product.skus.each do |sku|
        sheet.add_row ["#{product.name}", "#{product.name_en}", "#{sku.name}", "#{sku.name_en}", "#{sku.code}\t", "#{sku.upc_code}\t", "#{sku.ean_code}\t", "#{sku.china_code}\t", sku.fn_code, sku.fn_code_euro, sku.fn_code_uk, sku.fn_code_japan],
          style: [data, data, data, data, data, data, data, data, data, data, data, data]
      end
    end
  end
end
