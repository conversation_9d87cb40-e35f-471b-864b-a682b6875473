<%= form_with(model: product) do |form| %>
  <div class="mb-3">
    <%= form.label :name, class: "form-label" do %>
      <span style="color:red">*</span> 货品名称
    <% end %>
    <%= form.text_field :name, class: "form-control" %>
    <%= form.label :name, class: "form-label" do %>
      货品英文名
    <% end %>
  <%= form.text_field :name_en, class: "form-control" %>
  </div>

  <h5 class="text-xl mt-4">商品属性</h5>
  <div class="my-2" data-controller="nested-rondo">
    <div data-nested-rondo-target="fieldContain">
      <%= form.fields_for :skus do |sku| %>
        <%= render "sku_fields", f: sku %>
      <% end %>
    </div>
    <div class="links">
      <%= link_to_add_association "添加SKU", form, :skus %>
    </div>
  </div>

  <div>
    <%= form.submit "保存", class: "btn btn-success" %>
  </div>
<% end %>
