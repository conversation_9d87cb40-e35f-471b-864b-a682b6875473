<div class="container">
  <% if notice.present? %>
    <div class="alert alert-success mt-2" role="alert">
      <%= notice %>
    </div>
  <% end %>
  <div class="d-flex mt-3" style="justify-content: space-between">
    <div><h3>商品管理</h3></div>
    <div>
      <%= link_to "新建商品", new_product_path, class: "btn btn-secondary" %>
    </div>
  </div>

  <div id="products">
    <!-- 搜索 -->
    <div class="d-flex mb-3 mt-3">
      <%= form_with model: @q, local: true, url: products_path, method: :get do |form| %>
        <div class="row g-3 align-items-center">
          <div class="col-auto">
            <%= form.text_field :name_cont, value: params['q']&.[]('name_cont'), class: 'form-control', placeholder: '货品名称' %>
          </div>
          <div class="col-auto">
            <%= form.submit "搜索", class: 'btn btn-primary', style: "margin-right: 10px" %>
          </div>
        </div>
      <% end %>
      <%= form_for @q, url: products_path(format: :xlsx), method: :get do |f| %>
        <div class="form-inputs">
          <%= f.hidden_field :name_cont, value: params[:q] ? params[:q][:name_cont].to_s : nil %>
        </div>

        <div class="form-actions">
          <%= f.submit "批量导出", class: 'btn btn-success' %>
        </div>
      <% end %>
      <div style="flex: 1;text-align:right">
        <%= form_with local: true, url: import_products_path do |form| %>
          <div class="row g-3 align-items-center float-end">
            <div class="col-auto">
              <%= link_to "下载导入模板", download_import_template_products_path %>
            </div>
            <div class="col-auto">
              <%= form.file_field :file, class: 'form-control' %>
            </div>
            <div class="col-auto">
              <%= form.submit "导入商品", class: 'btn btn-primary' %>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <table class="table">
      <thead>
        <tr>
          <th>货品名称</th>
          <th>操作</th>
        </tr>
      </thead>

      <tbody>
        <% @products.each do |product| %>
          <tr>
            <td><%= product.name %></td>
            <td style="width: 90px">
              <%= link_to "编辑", edit_product_path(product) %>
              <%= link_to "删除", product, data: { turbo_confirm: "确定要删除吗？", turbo_method: :delete } %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
    <!-- 分页 -->
    <%= paginate @products %>
  </div>
</div>